* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
    cursor: none;
}

/* Custom Cursor */
.cursor-follow {
    position: fixed;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, #00ffff, #ff00ff);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transform: translate(-50%, -50%);
    transition: all 0.1s ease;
    opacity: 0.8;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 20px 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo {
    position: relative;
}

.logo-text {
    font-family: 'Orbitron', monospace;
    font-size: 24px;
    font-weight: 900;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

.logo-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    filter: blur(10px);
    opacity: 0.3;
    z-index: -1;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
}

.particle-field {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #00ffff, transparent),
        radial-gradient(2px 2px at 40px 70px, #ff00ff, transparent),
        radial-gradient(1px 1px at 90px 40px, #ffff00, transparent),
        radial-gradient(1px 1px at 130px 80px, #00ff00, transparent),
        radial-gradient(2px 2px at 160px 30px, #ff0080, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particles 20s linear infinite;
}

.wave-animation {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z' fill='%23ff00ff' fill-opacity='0.1'%3E%3C/path%3E%3C/svg%3E") repeat-x;
    animation: wave 10s ease-in-out infinite;
}

.hero-content {
    text-align: center;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 20px;
}

.title-line {
    display: block;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 4s ease infinite;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #cccccc;
    margin-bottom: 40px;
    animation: fadeInUp 1s ease 0.5s both;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.7s both;
}

.btn-primary, .btn-secondary {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    color: #000;
}

.btn-secondary {
    background: transparent;
    color: #ffffff;
    border: 2px solid #ffffff;
}

.btn-primary:hover, .btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.hero-video {
    position: absolute;
    right: 5%;
    top: 50%;
    transform: translateY(-50%);
    width: 300px;
    height: 200px;
    z-index: 2;
}

.video-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-placeholder:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.3);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.play-icon {
    width: 0;
    height: 0;
    border-left: 20px solid #000;
    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    margin-left: 5px;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-placeholder:hover .video-overlay {
    opacity: 1;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #cccccc;
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 2px;
    height: 30px;
    background: linear-gradient(to bottom, transparent, #00ffff);
    margin-bottom: 10px;
}

/* Featured Section */
.featured {
    padding: 100px 0;
    background: linear-gradient(135deg, #0a0a0a, #1a1a2e);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    text-align: center;
    margin-bottom: 60px;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.featured-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 20px;
    height: 600px;
}

.featured-item {
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.featured-item.large {
    grid-row: 1 / 3;
}

.featured-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
}

.item-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-item:hover .item-image img {
    transform: scale(1.1);
}

.item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    display: flex;
    align-items: flex-end;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.featured-item:hover .item-overlay {
    opacity: 1;
}

.item-info h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    color: #ffffff;
}

.item-info p {
    color: #cccccc;
    margin-bottom: 15px;
}

.play-btn-small {
    width: 40px;
    height: 40px;
    background: rgba(0, 255, 255, 0.8);
    border-radius: 50%;
    position: relative;
}

.play-btn-small::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 55%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    border-left: 10px solid #000;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
}

/* Categories Section */
.categories {
    padding: 100px 0;
    background: #0a0a0a;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.category-card {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
    border-color: #00ffff;
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.category-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #ffffff;
}

.category-card p {
    color: #cccccc;
    line-height: 1.6;
}

/* Stats Section */
.stats {
    padding: 100px 0;
    background: linear-gradient(135deg, #1a1a2e, #0a0a0a);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    text-align: center;
}

.stat-item {
    padding: 30px;
}

.stat-number {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
}

.stat-label {
    color: #cccccc;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Footer */
.footer {
    background: #000000;
    padding: 60px 0 20px;
    border-top: 1px solid rgba(0, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3, .footer-section h4 {
    margin-bottom: 20px;
    color: #ffffff;
}

.footer-section p {
    color: #cccccc;
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #00ffff;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 255, 255, 0.3);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: #888888;
}

/* Animations */
@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes particles {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

@keyframes wave {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(-50px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-video {
        display: none;
    }
    
    .featured-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        height: auto;
    }
    
    .featured-item.large {
        grid-row: auto;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary, .btn-secondary {
        width: 200px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}